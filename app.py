from flask import Flask, request, jsonify
from tronpy import Tron
from tronpy.keys import PrivateKey
import requests
import json

app = Flask(__name__)

# Tron Setup
client = Tron(network='nile')
owner_private_key = PrivateKey.fromhex("YOUR_OWNER_PRIVATE_KEY")
contract_address = "TN...YourContractAddress..."
contract = client.get_contract(contract_address)

def get_usdt_price():
    try:
        response = requests.get("https://api.coingecko.com/api/v3/simple/price?ids=tether&vs_currencies=usd")
        return response.json().get("tether", {}).get("usd", 0)
    except:
        return 0

@app.route("/mint", methods=["POST"])
def mint_token():
    data = request.get_json()
    recipient = data.get("recipient")
    amount = int(float(data.get("amount")) * 10**6)

    txn = (
        contract.functions.mint(recipient, amount)
        .with_owner(owner_private_key.public_key.to_base58check_address())
        .fee_limit(10_000_000)
        .build()
        .sign(owner_private_key)
    )
    result = txn.broadcast().wait()
    price = get_usdt_price()

    return jsonify({
        "status": "success",
        "txid": result["id"],
        "current_price_usd": price
    })

@app.route("/price", methods=["GET"])
def price():
    price = get_usdt_price()
    return jsonify({"usdt_price_usd": price})

if __name__ == "__main__":
    app.run(debug=True)
