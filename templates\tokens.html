<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Generated Tokens</title>
  <style>
    body { font-family: Arial; padding: 2rem; background: #f5f5f5; }
    table { width: 100%; border-collapse: collapse; background: white; margin-top: 2rem; }
    th, td { border: 1px solid #ccc; padding: 0.5rem; text-align: left; }
    th { background: #eee; }
    .expired { background: #ffcccc; }
    .valid { background: #ccffcc; }
    a { text-decoration: none; color: #00a; }
  </style>
</head>
<body>
  <h2>All Generated Off-Chain Tokens</h2>
  <a href="/">← Back to Generator</a>
  <table>
    <tr>
      <th>Token ID</th>
      <th>Amount</th>
      <th>Receiver</th>
      <th>Issued At</th>
      <th>Expires At</th>
      <th>Status</th>
    </tr>
    {% for token in tokens %}
    <tr class="{{ token.status }}">
      <td>{{ token.token_id }}</td>
      <td>{{ token.amount }}</td>
      <td>{{ token.receiver }}</td>
      <td>{{ token.issued_at }}</td>
      <td>{{ token.expires_at }}</td>
      <td>{{ token.status }}</td>
    </tr>
    {% endfor %}
  </table>
</body>
</html>
